import request from '@/utils/request'

export function uploadFile(data) {
  return request({
    url: '/file/upload/file',
    method: 'post',
    data
  })
}

export function uploadBatchFiles(data) {
  return request({
    url: '/file/upload/batchFiles',
    method: 'post',
    data
  })
}

export function getClassificationList() {
  return request({
    url: '/topic/getClassificationList',
    method: 'post'
  })
}

export function getTopicList(data) {
  return request({
    url: '/topic/searchTopicList',
    method: 'post',
    data
  })
}

export function changeStatus(data) {
  return request({
    url: '/topic/changeStatus',
    method: 'post',
    data
  })
}

export function topicDelete(query) {
  return request({
    url: '/topic/delete',
    method: 'get',
    params: query
  })
}

export function saveTopic(data) {
  return request({
    url: '/topic/saveTopic',
    method: 'post',
    data
  })
}

export function detailTopic(query) {
  return request({
    url: '/topic/detail',
    method: 'post',
    params: query
  })
}

export function getPostList(data) {
  return request({
    url: '/post/searchPostList',
    method: 'post',
    data
  })
}

export function verifyPost(data) {
  return request({
    url: '/post/approvePost',
    method: 'post',
    data
  })
}

export function deletePost(data) {
  return request({
    url: '/post/deletePost',
    method: 'post',
    data
  })
}

export function postChangeStatus(data) {
  return request({
    url: '/post/changeStatus',
    method: 'post',
    data
  })
}

export function getPostDetail(query) {
  return request({
    url: '/post/getPostDetail',
    method: 'post',
    params: query
  })
}

export function getPostCommentaryList(data) {
  return request({
    url: '/post/getPostCommentaryList',
    method: 'post',
    data
  })
}

export function getCommentaryList(data) {
  return request({
    url: '/postCommentary/searchCommentaryList',
    method: 'post',
    data
  })
}

export function deleteCommentary(data) {
  return request({
    url: '/postCommentary/deleteCommentary',
    method: 'post',
    data
  })
}

export function verifyCommentary(data) {
  return request({
    url: '/postCommentary/approveCommentary',
    method: 'post',
    data
  })
}

export function approvePost(data) {
  return request({
    url: '/post/approvePost',
    method: 'post',
    data
  })
}

// 商品列表
export function getGoodsList(data) {
  return request({
    url: '/topic/searchGoodsList',
    method: 'post',
    data
  })
}

export function getIsTopFlag(data) {
  return request({
    url: '/topic/getIsTopFlag',
    method: 'post',
    data
  })
}

export function getCommentaryDetail(query) {
  return request({
    url: '/postCommentary/getCommentaryDetail',
    method: 'post',
    params: query
  })
}

export function searchTopicStatisticsList(data) {
  return request({
    url: '/dataStatistics/searchTopicStatisticsList',
    method: 'post',
    data
  })
}

export function searchTopicReleaseStatisticsList(data) {
  return request({
    url: '/dataStatistics/searchTopicReleaseStatisticsList',
    method: 'post',
    data
  })
}

export function searchTopicUserStatisticsList(data) {
  return request({
    url: '/dataStatistics/searchTopicUserStatisticsList',
    method: 'post',
    data
  })
}

//红娘网数据同步按天分页查询接口
export function dataSyncDateCount(data) {
  return request({
    url: '/dataStatistics/dataSyncDateCount',
    method: 'post',
    data
  })
}

// 红娘网数据同步汇总查询接口
export function dataSyncCount(data) {
  return request({
    url: '/dataStatistics/dataSyncCount',
    method: 'post',
    data
  })
}

export function searchAllTopicList(data) {
  return request({
    url: '/topic/searchAllTopicList',
    method: 'post',
    data
  })
}

export function searchOpTopicList(data) {
  return request({
    url: '/topic/searchOpTopicList',
    method: 'post',
    data
  })
}

export function searchUserStatisticsList(query) {
  return request({
    url: '/dataStatistics/searchUserStatisticsList',
    method: 'post',
    params: query
  })
}

export function searchOpTopicListByStatus(query) {
  return request({
    url: '/topic/searchTopicNameList',
    method: 'post',
    data: query
  })
}

// 置顶
export function setTop(query) {
  return request({
    url: '/post/setTop',
    method: 'post',
    params: query
  })
}
// 获取帖子审核记录
export function getPostDetailApplys(query) {
  return request({
    url: '/post/getPostDetailApplys',
    method: 'post',
    params: query
  })
}
// 获取帖子审核记录
export function getCommentaryDetailApplys(query) {
  return request({
    url: '/postCommentary/getCommentaryDetailApplys',
    method: 'post',
    params: query
  })
}

// 帖子列表导出
export function exportPostList(query) {
  return request({
    url: '/post/exportPostList',
    method: 'post',
    data: query
  })
}
// 评论列表导出
export function exportCommentaryList(query) {
  return request({
    url: '/postCommentary/exportCommentaryList',
    method: 'post',
    data: query
  })
}
// 取消置顶
export function cancelTop(query) {
  return request({
    url: '/post/cancelTop',
    method: 'post',
    params: query
  })
}
// 帖子加精置顶状态修改
export function updateTop(query) {
  return request({
    url: '/post/updateTop',
    method: 'post',
    data: query
  })
}

// 排行榜
export function rankList(query) {
  return request({
    url: '/topic/rankList',
    method: 'post',
    data: query
  })
}
// 拉黑
export function topicBlock(query) {
  return request({
    url: '/topic/block',
    method: 'post',
    data: query
  })
}

// 话题列表不分页
export function searchAllTopicLists(query) {
  return request({
    url: 'topic/searchAllTopicLists',
    method: 'post',
    data: query
  })
}

export default {
  uploadFile,
  getClassificationList,
  getTopicList,
  changeStatus,
  topicDelete,
  saveTopic,
  detailTopic,
  getPostList,
  verifyPost,
  deletePost,
  postChangeStatus,
  getPostDetail,
  getPostCommentaryList,
  getCommentaryList,
  deleteCommentary,
  verifyCommentary,
  approvePost,
  getGoodsList,
  getIsTopFlag,
  getCommentaryDetail,
  searchTopicStatisticsList,
  searchTopicReleaseStatisticsList,
  searchTopicUserStatisticsList,
  searchAllTopicList,
  searchUserStatisticsList,
  setTop,
  cancelTop,
  updateTop,
  getPostDetailApplys,
  getCommentaryDetailApplys,
  exportPostList,
  exportCommentaryList,
  rankList,
  topicBlock,
  searchAllTopicLists,
  searchOpTopicList,
  searchOpTopicListByStatus
}
