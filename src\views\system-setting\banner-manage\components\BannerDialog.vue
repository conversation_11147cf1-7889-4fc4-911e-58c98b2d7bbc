<template>
  <el-dialog
    :title="dialogStatus === 'create' ? '新增Banner' : '编辑Banner'"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="120px"
      style="width: 100%; margin-left:10px;"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="展位" prop="buoyLocation">
            <el-select
              v-model="temp.buoyLocation"
              placeholder="请选择展位"
              style="width: 100%"
              :disabled="dialogStatus === 'update'"
              @change="handleBuoyLocationChange"
            >
              <el-option label="头图" value="1" />
              <el-option label="精选活动" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="contentType">
            <el-select
              v-model="temp.contentType"
              placeholder="请选择类型"
              style="width: 100%"
              :disabled="dialogStatus === 'update'"
              @change="handleContentTypeChange"
            >
              <el-option label="活动" :value="1" />
              <el-option label="广告" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 当展位和类型都选择后显示其余字段 -->
      <template v-if="showOtherFields">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="名称" prop="contentTitle">
              <el-select
                v-if="temp.contentType === 1"
                v-model="temp.contentTitle"
                placeholder="请选择活动"
                style="width: 100%"
                filterable
                remote
                :remote-method="searchActivities"
                :loading="activityLoading"
                @change="handleActivitySelect"
              >
                <el-option
                  v-for="item in activityOptions"
                  :key="item.id"
                  :label="item.actTitle"
                  :value="item.actTitle"
                  :data-id="item.id"
                />
              </el-select>
              <el-input
                v-else
                v-model="temp.contentTitle"
                placeholder="请输入广告名称（最多30个字）"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 标题字段（头图+活动 和 头图＋广告时不显示） -->
        <el-row :gutter="20" v-if="!((temp.buoyLocation === '1' && temp.contentType === 1)||(temp.buoyLocation === '1' && temp.contentType === 2))">
          <el-col :span="24">
            <el-form-item label="标题" prop="title" required>
              <el-input
                v-model="temp.title"
                placeholder="请输入标题（最多30个字）"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 副标题字段（头图+活动 和 头图+广告时不显示） -->
        <el-row :gutter="20" v-if="!((temp.buoyLocation === '1' && temp.contentType === 1)||(temp.buoyLocation === '1' && temp.contentType === 2))">
          <el-col :span="24">
            <el-form-item label="副标题" prop="subTitle">
              <el-input
                v-model="temp.subTitle"
                placeholder="请输入副标题（最多30个字）"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="用户端排序" prop="sort">
              <el-input-number
                v-model="temp.sort"
                :min="0"
                :step="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="temp.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="图片" prop="contentImg">
              <el-upload
                class="upload-demo"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :file-list="fileList"
                list-type="picture"
                :limit="1"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">
                  支持jpg、png、JPEG、gif、svg格式，单个文件不超过2M
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 跳转链接字段（活动时不显示） -->
        <el-row :gutter="20" v-if="temp.contentType !== 1">
          <el-col :span="24">
            <el-form-item label="跳转链接" prop="contentUrl">
              <el-input
                v-model="temp.contentUrl"
                placeholder="请输入跳转链接（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上架开始时间" prop="upShelfStartTime">
              <el-date-picker
                v-model="temp.upShelfStartTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上架结束时间" prop="upShelfEndTime">
              <el-date-picker
                v-model="temp.upShelfEndTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="temp.remark"
                type="textarea"
                placeholder="请输入备注（最多200个字）"
                maxlength="200"
                show-word-limit
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addHomePage, modifyHomePage } from '@/api/homePage'
import { getActivityList } from '@/api/actManage'
import { getToken } from '@/utils/auth'
import { getFileUrl } from '@/utils/common'

export default {
  name: 'BannerDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    dialogStatus: {
      type: String,
      default: 'create'
    }
  },
  data() {
    // 自定义title字段校验函数
    const validateTitle = (rule, value, callback) => {
      // 只有当标题字段显示时才进行必填校验
      if (!((this.temp.buoyLocation === '1' && this.temp.contentType === 1) || 
           (this.temp.buoyLocation === '1' && this.temp.contentType === 2))) {
        if (!value || value.trim() === '') {
          callback(new Error('请输入标题'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    
    return {
      dialogVisible: false,
      temp: {
        buoyLocation: '',
        contentType: '',
        contentTitle: '',
        title: '',
        subTitle: '',
        sort: 0,
        status: 1,
        contentImg: '',
        contentUrl: '',
        upShelfStartTime: '',
        upShelfEndTime: '',
        remark: '',
        actId: '' // 新增活动ID字段
      },
      rules: {
        buoyLocation: [
          { required: true, message: '请选择展位', trigger: 'change' }
        ],
        contentType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        contentTitle: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入用户端排序', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        title: [
          { validator: validateTitle, trigger: 'blur' }
        ],
        contentImg: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        upShelfStartTime: [
          { required: true, message: '请选择上架开始时间', trigger: 'change' }
        ],
        upShelfEndTime: [
          { required: true, message: '请选择上架结束时间', trigger: 'change' }
        ],
        actId: [
          { required: true, message: '请选择活动', trigger: 'change' }
        ]
      },
      activityOptions: [],
      activityLoading: false,
      fileList: [],
      uploadUrl: (process.env.VUE_APP_BASE_API || '') + '/file/upload/file',
      uploadHeaders: {
        'X-Auth-Token': getToken() || ''
      }
    }
  },
  computed: {
    showOtherFields() {
      return this.temp.buoyLocation !== '' && this.temp.contentType !== ''
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initForm()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    initForm() {
      // 先清空文件列表，避免图片跳动
      this.fileList = []

      if (this.dialogStatus === 'create') {
        // 新增模式：重置所有字段
        this.temp = {
          buoyLocation: '',
          contentType: '',
          contentTitle: '',
          title: '',
          subTitle: '',
          sort: 0,
          status: 1,
          contentImg: '',
          contentUrl: '',
          upShelfStartTime: '',
          upShelfEndTime: '',
          remark: '',
          actId: ''
        }
        // 清空活动选项
        this.activityOptions = []
      } else {
        // 编辑模式：使用表单数据，但保持字段显示逻辑与新增一致
        this.temp = Object.assign({}, this.formData)

        // 处理状态值，确保与radio组件的label值类型一致
        if (typeof this.temp.status === 'string') {
          if (this.temp.status === '生效' || this.temp.status === '1') {
            this.temp.status = 1
          } else if (this.temp.status === '失效' || this.temp.status === '0') {
            this.temp.status = 0
          }
        } else {
          this.temp.status = Number(this.temp.status)
        }

        // 处理时间格式
        this.formatDateTime('upShelfStartTime')
        this.formatDateTime('upShelfEndTime')

        // 如果是活动类型，需要加载活动选项
        if (this.temp.contentType === 1) {
          this.searchActivities('').then(() => {
            // 确保当前选中的活动在选项中
            if (this.temp.actId && this.activityOptions.length > 0) {
              const currentActivity = this.activityOptions.find(item => item.id === this.temp.actId)
              if (currentActivity) {
                this.temp.contentTitle = currentActivity.actTitle
              }
            }
          })
        }

        // 延迟设置文件列表，避免图片跳动
        this.$nextTick(() => {
          if (this.temp.contentImg) {
            this.fileList = [{
              name: 'image',
              url: getFileUrl(this.temp.contentImg),
              uid: Date.now() // 添加唯一标识
            }]
          }
        })
      }
    },

    // 格式化时间的辅助方法
    formatDateTime(fieldName) {
      if (this.temp[fieldName]) {
        try {
          const date = new Date(this.temp[fieldName])
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            const hours = String(date.getHours()).padStart(2, '0')
            const minutes = String(date.getMinutes()).padStart(2, '0')
            const seconds = String(date.getSeconds()).padStart(2, '0')
            this.temp[fieldName] = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
          }
        } catch (error) {
          console.error(`${fieldName}时间格式处理错误:`, error)
        }
      }
    },
    handleBuoyLocationChange() {
      // 展位改变时，根据新的展位类型清空不应该显示的字段
      // 头图模式：清空标题、副标题
      if (this.temp.buoyLocation === '1') {
        this.temp.title = ''
        this.temp.subTitle = ''
      }
      // 精选活动模式：如果之前是头图模式，可能需要恢复一些字段的显示
      // 但不自动填充，让用户手动输入
    },

    handleContentTypeChange() {
      // 类型改变时，清空相关字段
      this.temp.contentTitle = ''
      this.temp.actId = ''

      // 根据类型和展位组合清空对应字段
      if (this.temp.contentType === 1) {
        // 选择活动类型时，清空跳转链接（活动不需要跳转链接）
        this.temp.contentUrl = ''
        this.searchActivities('')

        // 如果是头图+活动，清空标题和副标题
        if (this.temp.buoyLocation === '1') {
          this.temp.title = ''
          this.temp.subTitle = ''
        }
      } else if (this.temp.contentType === 2) {
        // 选择广告类型时，清空活动相关字段
        this.temp.actId = ''
        this.activityOptions = []

        // 如果是头图+广告，清空标题和副标题
        if (this.temp.buoyLocation === '1') {
          this.temp.title = ''
          this.temp.subTitle = ''
        }
      }
    },
    // 搜索活动（获取上架活动列表）
    async searchActivities(query) {
      if (this.temp.contentType !== 1) return

      this.activityLoading = true
      try {
        const params = {
          pageNum: 1,
          pageSize: 100, // 获取足够多的活动
          actStatus: 1, // 只获取上架状态的活动
          actTitle: query || '', // 搜索关键词
          delFlag: '0'
        }

        const response = await getActivityList(params)
        if (response.code === 200 && response.data && response.data.list) {
          this.activityOptions = response.data.list
        } else {
          this.activityOptions = []
        }

        return Promise.resolve() // 返回Promise以便在initForm中使用
      } catch (error) {
        console.error('搜索活动失败:', error)
        this.$message.error('获取活动列表失败')
        this.activityOptions = []
        return Promise.reject(error)
      } finally {
        this.activityLoading = false
      }
    },
    // 处理活动选择
    handleActivitySelect(selectedValue) {
      const selectedActivity = this.activityOptions.find(item => item.actTitle === selectedValue)
      if (selectedActivity) {
        this.temp.actId = selectedActivity.id

        // 根据展位类型决定是否自动填充标题和副标题
        // 头图模式（无论活动还是广告）都不自动填充标题和副标题
        // 只有精选活动模式才自动填充
        if (this.temp.buoyLocation === '0') {
          this.temp.title = selectedActivity.actTitle
          this.temp.subTitle = selectedActivity.actCaption || ''
        }
      }
    },
    beforeUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'].includes(file.type)
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isValidType) {
        this.$message.error('上传图片只能是 JPG、PNG、GIF、SVG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    handleUploadSuccess(response) {
      try {
        // 处理不同的响应格式
        if (response && response.data && response.data.url) {
          this.temp.contentImg = response.data.url
        } else if (response && response.url) {
          this.temp.contentImg = response.url
        } else {
          console.warn('上传响应格式异常:', response)
          this.$message.warning('图片上传成功，但获取地址失败')
          return
        }
        this.$message.success('图片上传成功')
      } catch (error) {
        console.error('处理上传响应失败:', error)
        this.$message.error('处理上传响应失败')
      }
    },
    handleUploadError(error) {
      console.error('图片上传失败:', error)
      this.$message.error('图片上传失败，请重试')
    },
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          // 验证结束时间必须大于开始时间
          if (new Date(this.temp.upShelfEndTime) <= new Date(this.temp.upShelfStartTime)) {
            this.$message.error('上架结束时间必须大于开始时间')
            return
          }

          // 验证活动类型时必须有活动ID
          if (this.temp.contentType === 1 && !this.temp.actId) {
            this.$message.error('请选择活动')
            return
          }

          // 验证标题字段（头图模式时不验证，精选活动模式时必须验证）
          if (this.temp.buoyLocation === '0') {
            if (!this.temp.title) {
              this.$message.error('请输入标题')
              return
            }
          }

          // 创建临时数据对象，用于提交
          const tempData = Object.assign({}, this.temp)
          
          // 在提交时将时间转换为ISO 8601格式
          try {
            if (tempData.upShelfStartTime) {
              const date = new Date(tempData.upShelfStartTime)
              if (!isNaN(date.getTime())) {
                tempData.upShelfStartTime = date.toISOString()
              }
            }
            if (tempData.upShelfEndTime) {
              const date = new Date(tempData.upShelfEndTime)
              if (!isNaN(date.getTime())) {
                tempData.upShelfEndTime = date.toISOString()
              }
            }
          } catch (error) {
            console.error('时间格式转换错误:', error)
          }

          if (this.dialogStatus === 'create') {
            addHomePage(tempData).then(() => {
              this.$message({
                type: 'success',
                message: '创建成功'
              })
              this.$emit('success')
            })
          } else {
            modifyHomePage(tempData).then(() => {
              this.$message({
                type: 'success',
                message: '更新成功'
              })
              this.$emit('success')
            })
          }
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
      // 重置表单
      this.$refs.dataForm.resetFields()
      // 清空文件列表和活动选项
      this.fileList = []
      this.activityOptions = []
      // 重置temp数据
      this.temp = {
        buoyLocation: '',
        contentType: '',
        contentTitle: '',
        title: '',
        subTitle: '',
        sort: 0,
        status: 1,
        contentImg: '',
        contentUrl: '',
        upShelfStartTime: '',
        upShelfEndTime: '',
        remark: '',
        actId: ''
      }
    }
  }
}
</script>

<style scoped>
.upload-demo {
  width: 100%;
}
</style>
