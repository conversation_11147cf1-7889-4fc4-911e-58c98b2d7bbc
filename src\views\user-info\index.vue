<template>
  <div>
    <RouteTitle />
    <!-- 条件查询 -->
    <div class="main-body">
      <div class="main-body-top">
        <!-- 页面条件搜索 -->
        <dynamic-query
          :data="queryConfig"
          @queryChanged="handleQueryDataChanged"
          @search="handleSearch"
          @handleReset="handleReset"
        />
        <!-- 页面列表数据 + 分页条 -->
        <dynamic-table
          :config="tableConfig"
          :data="tableData"
          :is-loading.sync="tableLoading"
          @pagination="handlePageChange"
        >
          <!-- 操作按钮 -->
          <template v-slot:operate="{ rowData }">
            <el-button
              type="text"
              @click="handleViewDetail(rowData)"
            >查看详情</el-button>
          </template>
        </dynamic-table>
      </div>
    </div>
    
    <!-- 用户详情弹窗 -->
    <UserDetailDialog
      :visible="detailDialogVisible"
      :user-id="currentUserId"
      :user-data="currentUserData"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script>
import RouteTitle from '@/components/RouteTitle'
import DynamicQuery from '@/components/dynamic/Query.vue'
import DynamicTable from '@/components/dynamic/Table.vue'
import { getCustList } from '@/api/custManageApi'
import UserDetailDialog from './components/UserDetailDialog'

export default {
  name: 'UserInfo',
  components: {
    RouteTitle,
    DynamicQuery,
    DynamicTable,
    UserDetailDialog
  },
  data() {
    return {
      // 查询配置
      queryConfig: {
        queryItem: [
          {
            type: 'text',
            label: '用户ID：',
            model: 'custUserId',
            placeholder: '请输入用户ID'
          },
          {
            type: 'text',
            label: '手机号：',
            model: 'mobile',
            placeholder: '请输入手机号'
          },
          {
            type: 'text',
            label: '客户号：',
            model: 'custNo',
            placeholder: '请输入客户号'
          },
          {
            type: 'text',
            label: '姓名：',
            model: 'name',
            placeholder: '请输入姓名'
          },
          {
            type: 'text',
            label: '身份证号：',
            model: 'custIdentNo',
            placeholder: '请输入身份证号'
          },
          {
            type: 'date',
            label: '首次报名时间：',
            model: 'dateRange',
            config: {
              type: 'datetimerange',
              format: 'yyyy-MM-dd HH:mm:ss',
              separator: '至',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              defaultTime: ['00:00:00', '23:59:59']
            }
          },
          {
            type: 'option'
          }
        ]
      },
      
      // 查询数据
      queryData: {
        custUserId: '',
        mobile: '',
        custNo: '',
        name: '',
        custIdentNo: '',
        dateRange: []
      },
      
      // 表格配置
      tableConfig: {
        tableColumn: [
          {
            prop: '',
            label: '序号',
            isIndex: true,
            width: 80
          },
          {
            prop: 'id',
            label: '用户ID',
            minWidth: 120
          },
          {
            prop: 'blurMobile',
            label: '手机号',
            minWidth: 120
          },
          {
            prop: 'registerCount',
            label: '参加活动次数',
            width: 120
          },
          {
            prop: 'custNo',
            label: '行内客户号',
            minWidth: 150
          },

          {
            prop: 'firstRegisterTime',
            label: '首次报名时间',
            minWidth: 160
          },
          {
            prop: '',
            label: '操作',
            slotName: 'operate',
            fixed: 'right',
            width: 120
          }
        ]
      },
      
      // 表格数据
      tableData: {
        list: [],
        pageTotal: 0,
        pageSize: 10,
        pageNum: 1
      },
      
      tableLoading: false,
      detailDialogVisible: false,
      currentUserId: '',
      currentUserData: {}
    }
  },
  
  created() {
    this.fetchData()
  },
  
  methods: {
    // 查询条件变化
    handleQueryDataChanged(data) {
      this.queryData = { ...data }
    },
    
    // 重置查询条件
    handleReset() {
      // 重置父组件的查询数据
      this.queryData = {
        custUserId: '',
        mobile: '',
        custNo: '',
        name: '',
        custIdentNo: '',
        dateRange: []
      }
      // 重置分页到第一页
      this.tableData.pageNum = 1
      // 重新获取数据
      this.fetchData()
    },
    
    // 搜索
    handleSearch() {
      this.tableData.pageNum = 1
      this.fetchData()
    },
    
    // 分页变化
    handlePageChange(pagination) {
      this.tableData.pageNum = pagination.page
      this.tableData.pageSize = pagination.pageSize
      this.fetchData()
    },
    
    // 获取数据
    fetchData() {
      this.tableLoading = true
      
      const params = {
        custUserId: this.queryData.custUserId || undefined,
        mobile: this.queryData.mobile || undefined,
        custNo: this.queryData.custNo || undefined,
        name: this.queryData.name || undefined,
        custIdentNo: this.queryData.custIdentNo || undefined,
        pageNum: this.tableData.pageNum,
        pageSize: this.tableData.pageSize
      }
      
      // 处理时间范围
      if (this.queryData.dateRange && this.queryData.dateRange.length === 2) {
        params.startTime = this.queryData.dateRange[0]
        params.endTime = this.queryData.dateRange[1]
      }
      
      getCustList(params).then(res => {
        this.tableData.list = res.data.list || []
        this.tableData.pageTotal = res.data.total || 0
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    
    // 查看详情
    handleViewDetail(row) {
      this.currentUserId = row.id
      this.currentUserData = row
      this.detailDialogVisible = true
    },
    
    // 关闭详情弹窗
    handleCloseDetail() {
      this.detailDialogVisible = false
      this.currentUserId = ''
      this.currentUserData = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.main-body {
  padding: 20px;
  
  .main-body-top {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
