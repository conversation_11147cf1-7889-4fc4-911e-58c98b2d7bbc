项目部署文档

部署到服务器
1. 将 `dist` 里的文件内容上传到 nginx.conf里root指定的路径 `/usr/local/backactivitysysfront`
2. 使用项目根目录的 `nginx.conf` 配置 Nginx
3. 重启 Nginx 服务


前端地址：http://localhost
API代理：`/activity-manager-api/` → `http://域名:端口/activity-manager-api/`

环境要求
Node.js >= 8.9   （v14.21.3）
npm >= 3.0.0     （9.8.1）
Nginx

nginx.conf配置文件

server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location / {
        root /usr/local/backactivitysysfront;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # 后端API转发
    location ^~/activity-manager-api/ {
        proxy_pass http://域名:端口/activity-manager-api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}