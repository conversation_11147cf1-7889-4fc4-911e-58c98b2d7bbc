<template>
  <el-dialog
    title="用户详情"
    :visible.sync="dialogVisible"
    width="800px"
    top="5vh"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="user-detail">
      <div class="detail-row">
        <div class="detail-item">
          <span class="label">用户ID：</span>
          <span class="value">{{ this.userId || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">用户头像：</span>
          <span class="value">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="头像" class="avatar">
            <span v-else>（图片地址）</span>
          </span>
        </div>
      </div>
      
      <div class="detail-row">
        <div class="detail-item red-text">
          <span class="label">姓名：</span>
          <span class="value">{{ userInfo.name || '-' }}</span>
        </div>
        <div class="detail-item red-text">
          <span class="label">身份证号：</span>
          <span class="value">{{ userInfo.custIdentNo || '-' }}</span>
        </div>
      </div>
      
      <div class="detail-row">
        <div class="detail-item">
          <span class="label">手机号：</span>
          <span class="value">{{ userInfo.mobile || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">参加活动次数：</span>
          <span class="value">{{ userInfo.registerCount || 0 }}</span>
        </div>
      </div>
      
      <div class="detail-row">
        <div class="detail-item red-text">
          <span class="label">年龄：</span>
          <span class="value">{{ userInfo.age || '-' }}</span>
        </div>
        <div class="detail-item red-text">
          <span class="label">性别：</span>
          <span class="value">{{ getGenderText(userInfo.gender) }}</span>
        </div>
      </div>
      
      <div class="detail-row">
        <div class="detail-item red-text">
          <span class="label">证件类型：</span>
          <span class="value">{{ userInfo.custIdentType || '-' }}</span>
        </div>
        <div class="detail-item blue-text">
          <span class="label">社区：</span>
          <span class="value">{{ detailInfo.community || '-' }}</span>
        </div>
      </div>
      
      <div class="detail-row">
        <div class="detail-item blue-text">
          <span class="label">地址：</span>
          <span class="value">{{ detailInfo.address || '-' }}</span>
        </div>
        <div class="detail-item blue-text">
          <span class="label">身高：</span>
          <span class="value">{{ detailInfo.height || '-' }}</span>
        </div>
      </div>
      
      <div class="detail-row">
        <div class="detail-item blue-text">
          <span class="label">体重：</span>
          <span class="value">{{ detailInfo.weight || '-' }}</span>
        </div>
        <div class="detail-item blue-text">
          <span class="label">学历：</span>
          <span class="value">{{ detailInfo.education || '-' }}</span>
        </div>
      </div>
      
      <div class="detail-row">
        <div class="detail-item blue-text">
          <span class="label">星座：</span>
          <span class="value">{{ detailInfo.starSigns || '-' }}</span>
        </div>
      </div>
      
      <div class="detail-row full-width">
        <div class="detail-item">
          <span class="label">其它记录时留存的用户信息：</span>
          <span class="value">{{ getOtherInfo() }}</span>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCustDetail } from '@/api/custManageApi'

export default {
  name: 'UserDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: ''
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      userInfo: {},
      detailInfo: {}
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.userId) {
        this.fetchUserDetail()
      }
    }
  },
  methods: {
    fetchUserDetail() {
      this.loading = true
      // 基本信息从父组件传递的用户数据获取
      this.userInfo = this.userData || {}

      // 获取详细信息
      getCustDetail(this.userId).then(res => {
        this.detailInfo = res.data || {}
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    
    getGenderText(gender) {
      if (gender === 1) return '男'
      if (gender === 2) return '女'
      return '-'
    },
    
    getOtherInfo() {
      if (this.detailInfo.userRegisterInfo) {
        return '（用户报名成功后更新，每次报名如发现不一致则更新后台数据，如报名如发现现不一致则更新后台数据、如发现现有新增数据则创建动态）'
      }
      return '-'
    },
    
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-detail {
  .detail-row {
    display: flex;
    margin-bottom: 20px;
    
    &.full-width {
      flex-direction: column;
    }
    
    .detail-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-right: 20px;
      
      &:last-child {
        margin-right: 0;
      }
      
      .label {
        min-width: 120px;
        font-weight: bold;
        color: #333;
      }
      
      .value {
        color: #666;
        word-break: break-all;
      }
      
      .avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
      }
      
      &.red-text .label {
        color: #f56c6c;
      }
      
      &.blue-text .label {
        color: #409eff;
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
