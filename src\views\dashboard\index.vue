<template>
    <div>
        <div class="welcome_tip">
            <div class="welcome_content">欢迎登录南京银行活动管理平台！</div>
        </div>
        <!-- <div v-if="overViewCheckPermission" class="tip_content">
      <div class="img">
        <div class="over_view">
          <div class="company_name">{{ accountInfo.platformName }}</div>
        </div>
      </div>

      <div class="accout_info">
        <div class="content">

          <el-button v-if="platformAccount_search_dtl" type="primary" class="button_position" @click="onOperate('sys')">查看余额详情</el-button>
          <div class="content_title">累计收入</div>
          <div class="content_amonut">￥{{ numFormat(accountInfo.sysBalAmt) }}</div>
        </div>
        <div class="content">
          <el-button v-if="platformAccount_search_dzbl_dtl" type="primary" class="button_position" @click="onOperate('bank')">查看余额详情</el-button>
          <div class="content_title">银行余额</div>
          <div class="content_amonut">￥{{ numFormat(accountInfo.bankBalAmt) }}</div>
        </div>
      </div>
    </div> -->
        <!-- <el-dialog
            title="设置密码"
            :visible.sync="dialogVisible"
            :modal-append-to-body="false"
            width="500px"
        >
            <el-form
                ref="updateForm"
                :model="updateForm"
                :rules="updateRules"
                class="form-width"
                auto-complete="on"
            >
                <div class="line" />
                <el-form-item prop="oldPassword" label="旧密码">
                    <el-input
                        v-model="updateForm.oldPassword"
                        :type="oldPwdType"
                        name="oldPassword"
                        auto-complete="on"
                        placeholder="请输入当前密码"
                        show-password
                    />
                </el-form-item>
                <el-form-item prop="password" label="新密码">
                    <el-input
                        v-model="updateForm.password"
                        :type="pwdType"
                        name="password"
                        auto-complete="on"
                        placeholder="新密码长度必须是8-20位, 包含字母，数字和特殊符号3种组合"
                        show-password
                    />
                </el-form-item>
                <el-form-item prop="confirmPassword" label="确认密码">
                    <el-input
                        v-model="updateForm.confirmPassword"
                        :type="confirmPwdType"
                        name="confirmPassword"
                        auto-complete="on"
                        placeholder="请确认新密码"
                        show-password
                    />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button v-preventReClick type="primary" @click="setPwd()"
                    >确定</el-button
                >
            </div>
        </el-dialog> -->
    </div>
</template>

<script>
// import { date } from 'jszip/lib/defaults'
import sysManageApi from "@/api/sysManageApi";
import checkPermission from "@/utils/permission";
import { numFormat } from "@/utils";
import JSEncrypt from "jsencrypt";
import userApi from "../../api/userApi";

export default {
    name: "Dashboard",
    data() {
        const validatePass = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请输入新密码"));
            } else {
                if (this.updateForm.confirmPassword !== "") {
                    this.$refs.updateForm.validateField("confirmPassword");
                }
                callback();
            }
        };
        const validateConfirmPass = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请再次输入密码"));
            } else if (value !== this.updateForm.password) {
                callback(new Error("两次输入密码不一致!"));
            } else {
                callback();
            }
        };
        return {
            platformAccount_search_dzbl_dtl: checkPermission([
                "platformAccount_search_dzbl_dtl",
            ]),
            platformAccount_search_dtl: checkPermission([
                "platformAccount_search_dtl",
            ]),
            overViewCheckPermission: checkPermission(["platformAccount"]),
            accountInfo: {},
            platformName: "",
            sysBalAmt: "",
            bankBalAmt: "",
            dialogVisible: false,
            pwdType: "password",
            oldPwdType: "password",
            confirmPwdType: "password",
            loading: false,
            updateForm: {
                password: "",
                oldPassword: "",
                confirmPassword: "",
            },
            updateRules: {
                password: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePass,
                    },
                ],
                oldPassword: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "旧密码不得为空",
                    },
                ],
                confirmPassword: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validateConfirmPass,
                    },
                ],
            },
        };
    },
    mounted() {
        if (this.$store.state.user.expireFlag) {
            this.dialogVisible = true;
            // console.log('dialogVisible', )
            history.pushState(null, null, document.URL);
            window.addEventListener("popstate", this.disableBrowserBack);
        } else {
            this.dialogVisible = false;
        }
        // if (this.overViewCheckPermission) {
        //   sysManageApi.getOverviewInfo().then(res => {
        //     const data = JSON.parse(JSON.stringify(res.data))

        //     this.accountInfo = data[0]
        //     this.platformName = this.accountInfo.platformName
        //   })
        // }
    },
    destroyed() {
        // 清除popstate事件 否则会影响到其他页面
        window.removeEventListener("popstate", this.disableBrowserBack, false);
    },

    methods: {
        numFormat,
        checkPermission,
        disableBrowserBack() {
            history.pushState(null, null, document.URL);
        },
        onOperate(type) {
            let path = "/arrival-details";
            if (type === "sys") {
                path = "/platform_transaction_details";
            }
            this.$router.push(path);
        },
        setPwd() {
            this.$refs.updateForm.validate((valid) => {
                if (valid) {
                    if (
                        this.updateForm.oldPassword ===
                        this.updateForm.confirmPassword
                    ) {
                        this.$message.error("新旧密码不能一致");
                        return;
                    }
                    if (
                        this.$store.getters.userInfo.userName ===
                        this.updateForm.confirmPassword
                    ) {
                        this.$message.error("密码不能和用户名一致");
                        return;
                    }
                    const regStr =
                        "^(?=.*[A-Za-z])(?=.*\\d)(?=.*[^\\da-zA-Z\\s]).{8,20}$";
                    var reg = new RegExp(regStr);
                    if (!reg.test(this.updateForm.confirmPassword)) {
                        this.$message.error(
                            "密码长度必须是8-20位, 包含字母，数字和特殊符号3种组合"
                        );
                        return;
                    }
                    userApi.getPublicKey().then((res) => {
                        var publicKey = res.data;
                        if (publicKey) {
                            const encryptStr = new JSEncrypt();
                            encryptStr.setPublicKey(publicKey);
                            const params = {
                                oldPwd: encryptStr.encrypt(
                                    this.updateForm.oldPassword
                                ),
                                newPwd: encryptStr.encrypt(
                                    this.updateForm.password
                                ),
                                oriNewPwd: encryptStr.encrypt(
                                    this.updateForm.confirmPassword
                                ),
                                userId: this.$store.getters.userInfo.id,
                            };

                            sysManageApi
                                .modifyPassword(params)
                                .then((res) => {
                                    if (res.code === 200) {
                                        this.$message.success("修改密码成功！");
                                        this.updateForm = {
                                            password: "",
                                            oldPassword: "",
                                            confirmPassword: "",
                                        };
                                        this.$store.dispatch("user/logout");
                                        this.$store.dispatch("user/updateFlag");
                                    } else {
                                        this.$message.error(res.msg);
                                    }
                                })
                                .catch((error) => {
                                    this.loading = false;
                                    this.$message.error(error.data.msg);
                                });
                        }
                    });
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.welcome_tip {
    background: #f0f8ff;
    border-radius: 8px;
    // width: 96%;
    margin: 20px;
    height: 220px;
    background-image: url("../../assets/makret/home_group.svg");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-shadow: 0px 2px 9px rgba(0, 0, 0, 0.17);
}
.welcome_content {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 700;
    font-size: 32px;
    line-height: 32px;
    /* identical to box height, or 100% */
    color: rgba(0, 0, 0, 0.9);
    padding-top: 82px;
    padding-left: 84px;
}

.welcome_en {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 24px;
    /* identical to box height, or 117% */
    margin-top: 16px;
    margin-left: 84px;

    color: #b7b7b7;
}
.img {
    width: 100%;
    height: 89px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-image: url("../../assets/makret/administration_bg.png");
    padding-top: 30px;
}
.over_view {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-image: url("../../assets/makret/Overview_bg.png");
    width: 70%;
    height: 100px;
    box-shadow: 0px 2px 9px rgba(36, 116, 255, 0.35);
    border-radius: 8px;
    margin: auto;
}
.tip_content {
    margin: 20px;
    background: #ffffff;
    box-shadow: 0px 2px 9px rgba(0, 0, 0, 0.17);
    border-radius: 8px;
    height: 303px;
}
.company_name {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 26px;
    padding-top: 30px;
    padding-left: 22px;
    color: rgba(255, 255, 255, 0.9);
}
.accout_info {
    width: 70%;
    height: 111px;
    margin: 55px auto 0;
    display: flex;
    justify-content: space-between;
}
.content {
    width: 48%;
    height: 111px;
    background-image: url("../../assets/makret/home_capital_bg.png");
    // background: #FFFFFF;
    // border: 1px solid #C6E4FF;
    border-radius: 8px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.content_title {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    /* or 150% */
    margin-top: 26px;
    margin-left: 30px;

    color: rgba(0, 0, 0, 0.4);
}
.content_amonut {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    font-size: calc(100vw * 32 / 1920);
    // line-height: 28px;
    margin-top: 10px;
    margin-left: 30px;

    color: rgba(0, 0, 0, 0.9);
}
.button_position {
    margin-top: 20px;
    margin-right: 28px;
    float: right;
}
/deep/ .el-dialog {
    // height: 200px;
    background: linear-gradient(180deg, #f0f8ff 0%, #ffffff 8.44%);
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    .el-dialog__header {
        height: 20px;
        padding-top: 10px;
        .el-dialog__headerbtn {
            // top: 10px;
            display: none;
        }
    }
    .el-dialog__footer {
        background-color: #fff;
        padding: 10px 20px;
        text-align: center;
    }
    .el-dialog__title {
        margin-top: 10px;
        margin-right: 20px;
        font-family: "Source Han Sans CN";
        font-style: normal;
        font-weight: 500;
        font-size: 18px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.9);
    }
    .el-dialog__body {
        padding: 10px 20px 20px;
        .line {
            margin-top: 20px;
            width: 100%;
            height: 0.5px;
            background-color: #e2e8f0;
            // box-shadow: 0px 2px 4px rgba(209, 221, 236, 0.8);
        }
    }
}
</style>
