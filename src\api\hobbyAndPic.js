import request from '@/utils/request'

// 下载模板
export function downTemplate(data) {
  return request({
    url: '/hobby/template',
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

// 上传文件
export function uploadInterest(data) {
  return request({
    url: '/hobby/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}

// 下载文件
export function downLoad(data) {
  return request({
    url: '/hobby/downLoad',
    method: 'post',
    data
  })
}

// 图片人工审核
export function imgAudit(data) {
  return request({
    url: '/pic/audit',
    method: 'post',
    data
  })
}

// 审核记录
export function getRecord(data) {
  return request({
    url: '/pic/record',
    method: 'post',
    data
  })
}
