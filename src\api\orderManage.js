import request from '@/utils/request'

// 从活动管理导入下载文件函数
import { downloadFile } from './actManage.js'

// 获取活动订单列表
export function getRegisterInfoList(data) {
  return request({
    url: '/act-manage/registerInfo/getRegisterInfoList',
    method: 'post',
    data
  })
}

// 订单签到
export function signOrder(id) {
  return request({
    url: '/act-manage/registerInfo/sign',
    method: 'get',
    params: { id }
  })
}

// 获取订单详情
export function getOrderDetail(id) {
  return request({
    url: '/act-manage/registerInfo/detail',
    method: 'get',
    params: { id }
  })
}

// 导出订单列表（获取文件名）
export function exportOrderList(data) {
  return request({
    url: '/act-manage/registerInfo/orderExcel',
    method: 'post',
    data
  })
}

// 导出并直接下载订单列表
export function exportAndDownloadOrderList(data) {
  return new Promise((resolve, reject) => {
    // 先调用导出接口获取文件名
    exportOrderList(data).then(res => {
      if (res.code === 200 && res.data) {
        const filename = res.data
        // 再调用下载接口下载文件（使用与活动管理相同的downloadFile函数）
        downloadFile(filename).then(downloadRes => {
          // 确保返回的是blob数据而不是整个响应对象
          resolve({
            data: downloadRes.data || downloadRes, // 优先使用response.data
            filename: filename
          })
        }).catch(downloadError => {
          reject(downloadError)
        })
      } else {
        reject(new Error(res.msg || '导出失败'))
      }
    }).catch(exportError => {
      reject(exportError)
    })
  })
}

// 获取活动统计报表
export function getActivityReport(data) {
  return request({
    url: '/act-manage/activity/getActivityReport',
    method: 'post',
    data
  })
}

// 导出活动统计报表（获取文件名）
export function exportActivityReport(data) {
  return request({
    url: '/act-manage/activity/actReportExcel',
    method: 'post',
    data
  })
}

// 导出并直接下载活动统计报表
export function exportAndDownloadActivityReport(data) {
  return new Promise((resolve, reject) => {
    // 先调用导出接口获取文件名
    exportActivityReport(data).then(res => {
      if (res.code === 200 && res.data) {
        const filename = res.data
        // 再调用下载接口下载文件（使用与活动管理相同的downloadFile函数）
        downloadFile(filename).then(downloadRes => {
          // 确保返回的是blob数据而不是整个响应对象
          resolve({
            data: downloadRes.data || downloadRes, // 优先使用response.data
            filename: filename
          })
        }).catch(downloadError => {
          reject(downloadError)
        })
      } else {
        reject(new Error(res.msg || '导出失败'))
      }
    }).catch(exportError => {
      reject(exportError)
    })
  })
}
